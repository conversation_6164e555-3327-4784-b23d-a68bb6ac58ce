Question #1
A customer has a database cluster running in a VCF cluster with the following characteristics:
40/60 Read/Write ratio

High IOPS requirement -
No contention on an all-flash OSA vSAN cluster in a VI Workload Domain
Which two vSAN configuration options should be configured for best performance? (Choose two.)

A. RAID 1
B. Deduplication and Compression enabled
C. RAID 5
D. Flash Read cache reservation
E. Deduplication and Compression disabled
 
Question #2
An architect is sizing the workloads that will run in a new VMware Cloud Foundation (VCF) Management Domain, the customer has a requirement to use Aria Operations to provide effective monitoring of the new VCF Solution.
What is the minimum Aria Operations Analytics node size requirement when Aria Suite Lifecycle is in VCF aware mode?

A. Extra Large
B. Small
C. Medium Most Voted
D. Large
 
Correct Answer: C
Community vote distribution
C (100%)

Question #3
Which statement defines the purpose of Business Requirements?

A. Business requirements define how the goals and objectives can be achieved.
B. Business requirements define which goals and objectives can be achieved.
C. Business requirements define what goals and objectives need to be achieved.
D. Business requirements define which audience needs to be involved.
 
Correct Answer: C

Question #4
A company will be expanding their existing VCF environment for a new application. The existing VCF environment currently has a management domain and two separate VI workload domains with different hardware profiles. The new application has the following requirements:
The application will use significantly more memory than current workloads today.
The application will have a limited number of licenses to run on hosts.
Additional VCF and hardware costs has been approved for the application.
The application will contain confidential customer information that requires isolation from other workloads.
What design recommendations should the administrator document?

A. Purchase enough matching hardware to accommodate the new applications memory requirements and expand an existing cluster to accommodate the new application. Use host affinity rules to manage the new licensing.
B. Enough identical hardware for the management domain should be ordered to accommodate the new application requirements and a new workload domain should be designed for the application.
C. Deploy a new consolidated VCF instance and deploy the new application into it.
D. A new Workload domain with hardware supporting the memory requirements of the new application should be implemented.
 
Correct Answer: D

Question #5
An architect is documenting the design for a new VMware Cloud Foundation solution. Which statement would be an example of a conceptual model for this solution?

A. A high-level overview of the solution, including risks, assumptions and constraints Most Voted
B. A detailed description of the VMware Cloud Foundation solution configuration, including host names and IP addresses
C. A detailed diagram of the interfaces of the NSX Edge components within the management domain in the data center
D. A. high-level diagram of the VMware Cloud Foundation solution showing the workload domains with the number of physical hosts per cluster
 
Correct Answer: A
Community vote distribution
A (80%)
D (20%)

Question #6
Due to limited budget and hardware, an administrator is constrained to a VMware Cloud Foundation (VCF) consolidated architecture of seven ESXi hosts in a single cluster. An application that consists of two virtual machines hosted on this infrastructure requires minimal disruption to storage IO during business hours.
Which two options that would be most effective in mitigating this risk without reducing availability? (Choose two.)

A. Enable fully automatic (Distributed Resource Scheduling) DRS policies on the cluster
B. Replace the vSAN shared storage exclusively with an All-Flash Fibre channel shared storage solution
C. Apply 100% CPU and Memory reservations on these virtual machines
D. Implement FTT=1 Mirror for this application virtual machine
E. Perform all host maintenance operations outside of business hours
 
Correct Answer: AE
Community vote distribution
DE (60%)
CE (40%)

Question #7
As part of a new VMware Cloud Foundation (VCF) deployment, a customer is planning to implement vSphere IaaS control plane.
What component could be installed and enabled to implement the solution?

A. Storage DRS
B. Aria Operations
C. NSX Edge networking Most Voted
D. Aria Automation
 
Correct Answer: C
Community vote distribution
C (60%)
D (40%)

Question #8
An architect is planning resources for a new cluster that will be integrated into an existing VI Workload Domain. The cluster's primary purpose is to support a mission-critical application with five resource-intensive virtual machines.
Which design recommendation should the architect provide to prevent resource bottlenecks while meeting the N+1 availability requirement and keeping the overall investment cost minimal?

A. Establish a cluster with four hosts and implement rules to prioritize resources for the application virtual machines.
B. Establish a cluster with three hosts and exclusively run the application virtual machines on this cluster.
C. Establish a cluster with six hosts and implement automated placement rules to keep the application virtual machines together.
D. Establish a cluster with six hosts and implement automated placement rules to distribute the application virtual machines. Most Voted
 
Correct Answer: A
Community vote distribution
A (67%)
D (33%)

Question #9
An architect was requested to recommend a solution for migrating 5000 VMs from an existing vSphere environment to a new VMware Cloud Foundation infrastructure.
Which feature or tool can be recommended by the architect to minimize the downtime and automate the process?

A. VMware HCX
B. Cross vCenter vMotion
C. VMware Converter
D. vSphere vMotion
 
Correct Answer: A

Question #10
An architect is tasked to updating the design for an existing VMware Cloud Foundation (VCF) deployment to include four vSAN ESA ready nodes.
The existing deployment compromises of the following:
Four homogenous vSAN ESXi ready nodes in the management domain
Four homogenous ESXi nodes with iSCSI principal storage in workload domain A
What should the architect recommend when including this additional capacity for application workloads?

A. Create a new vLCM baseline workload domain with the four new nodes
B. Create a new vLCM baseline cluster in the existing workload domain with the four new nodes
C. Create a new vLCM image workload domain with the four new nodes Most Voted
D. Commission the four new nodes into the existing workload domain A cluster
 
Correct Answer: B
Community vote distribution
C (100%)

Question #11
During a transformation project kick-off meeting, an architect highlights specific areas on which to focus while developing the new conceptual design.
Which statement is the business requirement?

A. Sites must support a network latency of less than 12 ms RTT.
B. There is no budget specifically assigned for disaster recovery.
C. The solution must continue to operate even in case of an entire datacenter failure.
D. The project should use the existing storage devices within the data center.
 
Correct Answer: C

Question #12
An architect is designing a VMware Cloud Foundation (VCF)-based private cloud solution for a customer. The customer has stated the following requirement:
All management tooling must be resilient against a single ESXi host failure
When considering the design decisions for VMware Aria Suite components, what should the Architect document to support the stated requirement?

A. The solution will deploy an external load balancer for Aria Operations Cloud Proxies.
B. The solution will deploy three Aria Automation appliances in a clustered topology.
C. The solution will deploy the VCF Workload domain in a stretched topology across two sites.
D. The solution will deploy Aria Suite Lifecycle in a clustered topology.
 
Correct Answer: B

Question #13
As part of the requirement gathering phase an architect identified following requirement for the newly deployed SDDC environment:
Reduce the network latency between two application virtual machines.
To meet that application owner's goal, which design decision should be included in the design?

A. Configure DRS rule to keep the application virtual machines on the same ESXi hosts.
B. Configure Storage DRS rule to keep the application virtual machines on different datastores.
C. Configure DRS rule to separate the application virtual machines to different ESXi hosts.
D. Configure Storage DRS rule to keep the application virtual machines on the same datastore.
 
Correct Answer: A

Question #14
A VMware Cloud Foundation multi-AZ (Availability Zone) design mandates that:
All management components are centralized.
The availability SLA must adhere to no less than 99.99%.
What would be the two design decisions that would help satisfy those requirements? (Choose two.)

A. Configure VMware Live Recovery between the selected AZ(s).
B. Choose two close proximity AZ(s) and configure a stretched management workload domain.
C. Choose two distant AZ(s) and configure distinct management workload domains.
D. Configure a stretched L2 VLAN for the infrastructure management components between the AZ(s).
E. Configure a separate VLAN for the infrastructure management components within each AZ.
 
Correct Answer: BD

Question #15
An architect is planning the deployment of Aria components in a VMware Cloud Foundation environment using SDDC Manager and must plan prepare a logical diagram with networking connections for particular Aria products.
Which are two valid Application Virtual Networks for Aria Operations deployment using SDDC Manager? (Choose two.)

A. Region-A - Overlay backed segment
B. Region-A - VLAN backed segment
C. X-Region - VLAN backed segment Most Voted
D. X-Region - Overlay backed segment Most Voted
 
Correct Answer: AB
Community vote distribution
CD (100%)

Question #16
An architect has been tasked with reviewing a VMware Cloud Foundation design document.
Observe the following requirements:
REQ01: The solution must support the private cloud cybersecurity industry and local standards and controls.
REQ02: The solution must ensure that the cloud services are transitioned to operation teams.
REQ03: The solution must provide a self-service portal.
REQ04: The solution must provide the ability to consume storage based on policies.
REQ05: The solution should provide the ability to extend networks between different availability zones.
Observe the following design decisions:
DD01: There will be a clustered deployment of Aria Automation.
DD02: There will be an integration between Aria Automation and multiple geo-located vCenter Servers.
Based on the information provided, which two requirements satisfy the stated design decisions? (Choose two.)

A. REQ01
B. REQ02
C. REQ04
D. REQ05 Most Voted
E. REQ03 Most Voted
 
Correct Answer: BE
Community vote distribution
DE (100%)

Question #17
An architect is preparing a VI Workload Domain design with dedicated NSX instance. The workload domain is planned to grow up to 300 ESXi hosts within the next six months.
Which is the minimum NSX Manager form factor that should be recommended by the architect for this VI Workload Domain to support the forecasted growth?

A. Medium
B. Extra small
C. Small
D. Large
 
Correct Answer: D

Question #18
When determining the compute capacity for a VMware Cloud Foundation VI Workload Domain, which three elements should be considered when calculating usable resources? (Choose three.)

A. Number of 10GBE NICs per VM
B. Number of VMs
C. Disk capacity per VM
D. VM swap file
E. vSAN space efficiency feature enablement
F. CPU/Cores per VM
 
Correct Answer: BDF

Question #19
During a requirement gathering workshop, various Business and Technical requirements were collected from the customer.
Which requirement would be categorized as a Business Requirement?

A. The application should be compatible with Windows, macOS, and Linux operating systems.
B. Data should be encrypted using AES-256 encryption.
C. Decrease processing time for service requests by 30%.
D. The system should support 10,000 concurrent users.
 
Correct Answer: C

Question #20
A customer has a requirement to improve bandwidth and reliability for traffic that is routed through the NSX Edges in VMware Cloud Foundation.
What should the architect recommend satisfying this requirement?

A. Configure a TEP Group for NSX Edges
B. Configure a LAG Group for NSX Edges
C. Configure a Load balanced Group for NSX Edges
D. Configure a TEP Independent Group for NSX Edges
 
Correct Answer: B

Question #21
An architect is designing a new VMware Cloud Foundation (VCF)-based Private Cloud solution. During the requirements gathering workshop, a network team stakeholder stated that:
The solution must ensure that any physical networking component has N+N redundancy.
The solution must ensure that inter-datacenter network links are diversely routed.
When documenting the design, how should the architect classify these requirements?

A. Recoverability
B. Manageability
C. Availability
D. Performance
 
Correct Answer: C

Question #22
An architect has been tasked with reviewing a VMware Cloud Foundation design document.
Observe the following requirements:
REQ01: The solution must provide the ability to request new tenant creation with multi-site and different size options.
REQ02: The solution must provide the capability to monitor the software-defined data center for capacity and performance.
REQ03: The solution must provide the ability to generate reports with customized metrics to meet business requests.
REQ04: The solution should report all capacity planning components (such as current capacity usage monthly and annual usage growth).
REQ05: The solution must provide the ability to provision new virtual machines from predefined templates.
REQ06: The solution must provide a self-service catalog for end-users to consume services.
Observe the following design decisions:
DD01: There will be a centralized deployment of Aria Operations Management.
DO02: There will customize super-metrics based on existing metrics.
Based on the stated requirements and design decisions, which three requirements does this design decisions satisfy? (Choose three.)

A. REQ04
B. REQ02
C. REQ05
D. REQ06
E. REQ01
F. REQ03
 
Correct Answer: ABF

Question #23
An organization is planning to expand their existing VMware Cloud Foundation (VCF) environment to meet an increased demand for new user facing applications. The physical host hardware proposed for the expansion is a different model compared the existing hosts, although it has been confirmed that both sets of hardware are compatible.
The expansion needs to provide capacity for management tooling workloads dedicated to the applications and it has been decided to deploy a new cluster within the management domain to host the workloads.
What should the architect include within the logical design for this design decision?

A. The design implication stating that the management tooling and the VCF management workloads have different purposes
B. The design assumption stating that the separate cluster will provide complete isolation for lifecycle management
C. The design justification stating that the separate cluster provides flexibility for manageability and connectivity of the workloads
D. The design qualities affected by the decision listed as Availability and Performance
 
Correct Answer: C

Question #24
A VMware Cloud Foundation design incorporates the following technical requirements:
1. All management components must have their login sessions timeout after 2 minutes of inactivity.
2. Communication between management components should be limited to required ports only.
3. Modifications required by compliancy should not impact the management components functionality.
What would be the recommendation from a design perspective that would aid in achieving the above requirements?

A. Apply NSX DFW (Distributed Firewall) to achieve zero-trust
B. Consult the Compliance Kit for VMware Cloud Foundation
C. Leverage the results of a vulnerability assessment and apply the recommendations
D. Consult the vSphere Security Configuration kit
 
Correct Answer: B

Question #25
An architect is working with a service provider to design a VMware Cloud Foundation (VCF) solution for hosting workloads for multiple tenants.
The following requirements were identified:
Each tenant must have full access to their own vCenter.
Each tenant will manage their own identity provider for access.
A total of 28 tenants will be onboarded.
Each tenant will have an independent VCF lifecycle maintenance schedule.
Which VCF architecture option will meet these requirements?

A. A single VCF instance with a standard architecture model and 28 isolated SSO domains.
B. Two VCF instances with a consolidated architecture model and 14 tenant clusters each.
C. A single VCF instance with a consolidated architecture model and 28 tenant clusters.
D. Two VCF instances with a standard architecture model and 14 isolated SSO domains each. Most Voted
 
Correct Answer: D
Community vote distribution
D (88%)
13%

Question #26
The following requirements were identified in an architecture workshop for a virtual infrastructure design project:
REQ001: All virtual machines must satisfy the Recovery Point Objective (RPO) of fifteen (15) minutes or less in a disaster recovery (DR) situation.
REQ002: Service level availability must satisfy 99,999% measured yearly.
Which two test cases will validate these requirements?

A. Simulate or invoke an outage of the primary datacenter. All virtual machines must not lose more than one (1) hour of data prior to the outage.
B. Simulate or invoke an outage of the primary datacenter. All virtual machines must not lose more than fifteen (15) minutes of data prior to the outage.
C. Simulate or invoke an outage of the primary datacenter. All virtual machines must be restored within fifteen (15) minutes or less.
D. Simulate or invoke an outage of the primary datacenter. All virtual machines must be restored within one (1) hour or less.
 
Correct Answer: BC

Question #27
A design requirement has been specified for a new VMware Cloud Foundation (VCF) instance. All managed workload resources must be lifecycle managed with the following criteria:
Development resources must be automatically reclaimed after two weeks
Production resources will be reviewed yearly for reclamation
Resources identified for reclamation must allow time for review and possible extension
What capability will satisfy the requirements?

A. Aria Automation Project Membership
B. Aria Automation Lease Policy
C. Aria Suite Lifecycle Content Management
D. Aria Operations Rightsizing Recommendations
 
Correct Answer: B

Question #28
An architect is evaluating a requirement for a Cloud Management self-service solution to offer its users the ability to migrate their own workloads using VMware vMotion.
Which component could the architect include in the solution design that will help satisfy the requirement?

A. Aria Operations
B. Aria Automation Orchestrator
C. Aria Lifecycle Manager
D. Aria Automation Config
 
Correct Answer: B

Question #29
The following design decisions that were made relating to storage design.
A storage policy that would support failure of a single fault domain being the server rack
Two vSAN OSA disk groups per host each consisting of four 4TB Samsung SSD capacity drives
Two vSAN OSA disk groups per host each consisting of a single 300GB Intel NVMe cache drive
Encryption at rest capable disk drives
Dual 10Gb or faster storage network adapters
Which two design decisions would an architect include within the physical design? (Choose two.)

A. Dual 10Gb or faster storage network adapters
B. Two vSAN OSA disk groups per host each consisting of a single 300GB Intel NVMe cache drive Most Voted
C. Encryption at rest capable disk drives
D. A storage policy that would support failure of a single fault domain being the server rack
E. Two vSAN OSA disk groups per host each consisting of four 4TB Samsung SSD capacity drives Most Voted
 
Correct Answer: AE
Community vote distribution
BE (100%)

Question #30
A customer has a requirement to use isolated domains in VMware Cloud Foundation but is constrained to a single NSX management pane.
What should the architect recommend satisfying this requirement?

A. NSX Federation
B. a 1:1 NSX Instance
C. an NSX VPC
D. a Shared NSX Instance Most Voted
 
Correct Answer: A
Community vote distribution
D (67%)
C (33%)

Question #31
An architect is designing a VMware Cloud Foundation (VCF)-based private cloud solution for a customer. The customer has stated the following requirement:
All components within the solution must be resilient to N+l.
During discovery, the following information has also been provided:
Over the next 3 years, due to various applications being retired, no overall growth in resource consumption is expected.
Following a review of a demand-based capacity report from Aria Operations, the architect has calculated that all of the existing workloads should fit into a 4-node cluster. Once all workloads are migrated, the resources of the cluster will be 90% utilized.
Given the information provided, a combination of which three design decisions satisfy the requirement? (Choose three.)

A. The solution will deploy a workload cluster consisting of four VMware vSphere hosts.
B. The solution will set the Host failures cluster tolerates for the workload cluster to 1.
C. The solution will deploy a workload cluster consisting of five VMware vSphere hosts.
D. The solution will set the DRS Automation level setting for the workload cluster to Partially Automated.
E. The solution will configure vSphere Dynamic Resource Scheduling (DRS) for the workload cluster.
F. The solution will configure vSphere High Availability (HA) for the workload cluster.
 
Correct Answer: BCF

Question #32
An architect is updating a design document in preparation for an expansion of their organization's existing VCF environment. Following the completion of a capacity assessment, a cluster will be expanded to support the hosting of future application deployments. Due to the length of time that has passed since the deployment of the existing solution, identical hardware is no longer available for purchase and a newer model from the same vendor has been selected.
What should the architect include within the design documentation based on this approach?

A. A constraint that the same hardware vendor as the existing hosts must be used
B. A requirement that the cluster must use diverse hardware vendors
C. A risk that identical hardware is no longer available for purchase
D. An assumption that the cluster will provide sufficient capacity for the applications
 
Correct Answer: C

Question #33
An Architect is designing a VMware Cloud Foundation (VCF)-based private cloud solution for a customer. During the requirements gathering workshop, the customer stated the following:
All users must only have access to the solution components to fulfil their defined role.
All administrative users must be authenticated to a separate approved identity source for administrator accounts only.
All service users must be authenticated to the central approved identity source.
All service account passwords must be stored centrally in an approved secrets management platform.
When creating the design, how should the Architect classify all the stated requirements?

A. Availability
B. Recoverability
C. Security
D. Manageability
 
Correct Answer: C

Question #34
A customer is designing a new VMware Cloud Foundation stretched cluster using L2 non-uniform connectivity, where due to a past incident an attacker was able to inject some false routes into their dynamic global routing table.
What design decision can be taken to prevent this when configuring the Tier-0 gateway?

A. Gateway Firewall with ECMP
B. BGP peer password
C. OSPF MD5 authentication
D. Implicit deny for any traffic
 
Correct Answer: C

Question #35
The following are a set of design decisions related to networking:
DD01: Set NSX Distributed Firewall (DFW) to Block all traffic by default.
DD02: Use VLANs to separate physical network functions.
DD03: Connect the management interface eth0 of each NSX Edge node to VLAN 100.
DD04: Deploy 2x 64-port Cisco Nexus 9300 switches for top of rack ESXi host connectivity.
Which design decision would an architect include in the logical design?

A. DD01
B. DD04
C. DD03
D. DD02 Most Voted
 
Correct Answer: A
Community vote distribution
D (67%)
A (33%)

Question #36
The following requirements were identified in an architecture workshop for a virtual infrastructure design project:
REQ001: All virtual machines must meet the Recovery Time Objective (RTO) of twenty-four hours or less in a disaster recovery (DR) scenario.
Which two test cases will verify these requirements?

A. Simulate or trigger an outage of the primary datacenter. All virtual machines must be restored within twenty-four hours or less. Most Voted
B. Simulate or trigger an outage of the primary datacenter. All virtual machines must not lose more than twenty-four hours of data prior to the outage. Most Voted
C. Simulate or trigger an outage of the primary datacenter. All virtual machines must be restored within four hours or less.
D. Simulate or trigger an outage of the primary datacenter. All virtual machines must not lose more than four hours of data prior to the outage.
 
Correct Answer: AC
Community vote distribution
AB (50%)
AD (50%)

Question #37
A customer defined a requirement for the newly deployed SDDC infrastructure which will host one of the applications responsible for video streaming. Application will run be as part of a VI Workload Domain with dedicated NSX instance and virtual machines Required network throughput was defined as a 250 Gb/s. Additionally the application should provide the lowest possible latency.
Which design decision should be recommended by an architect for the NSX Edge deployment?

A. Deploy 2 large NSX Edges using SDDC Manager.
B. Deploy NSX bare-metal Edges and create Edge Cluster using NSX console.
C. Deploy 2 NSX Edges using NSX console and add to Edge cluster created in SDDC Manager.
D. Deploy 4 extra large edges using vCenter Server console.
 
Correct Answer: B

Question #38
A VMware Cloud Foundation design is focused on IaaS control plane security, where the following requirements are present:
Support for Kubernetes Network Policies.
Cluster-wide network policy support.
Multiple Kubernetes distribution(s) support.
What would be the design decision that meets the requirements for VMware Container Networking?

A. NSX VPCs
B. Velero Operators
C. Antrea
D. Harbor
 
Correct Answer: C

Question #39
An architect is working with a customer to design a VMware Cloud Foundation (VCF) solution that is required for a highly secure infrastructure project that must remain separated from all other virtual infrastructure. The customer has already purchased six high density vSAN ready nodes and there is no budget for additional nodes to be added for the expected lifespan of this use case.
Assuming capacity is sized correctly, which VCF architecture model and topology should the Architect recommend?

A. Multiple Instance - Single Availability Zone Standard architecture model
B. Single Instance - Multiple Availability Zone Standard architecture model
C. Single Instance - Single Availability Zone Standard architecture model
D. Single Instance Consolidated architecture model Most Voted
 
Correct Answer: D
Community vote distribution
D (100%)

Question #40
As part of a VMware Cloud Foundation (VCF) design, an architect is responsible for planning for the migration of existing workloads using HCX to a new VCF environment.
Which two prerequisites would the architect require to complete the objective? (Choose two.)

A. DRS enabled within the VCF instance.
B. Service accounts for the applicable appliances.
C. Active Directory configured as an authentication source.
D. Extended IP spaces for all moving workloads.
E. NSX Federation implemented between the VCF instances.
 
Correct Answer: BD

Question #41
An architect is working with an organization on the creation of a new Private Cloud Platform. The organization has provided the following business objectives they wish to achieve with the new platform:
Reduce the operating costs associated with running separate areas of hosting capacity and separate/duplicate systems.
Reduce the risks, time and effort associated with managing platforms that are out of vendor support.
Reduce the operating costs associated with Public Cloud usage.
Reduce the risks associated with having incomplete documentation for application inventory and dependency mappings.
They have grouped these business objectives into a set of use cases:
Migration - Provide a platform that supports the migration of virtualized workloads from existing platforms.
Containerization - Provide a platform that supports the deployment of containerized workloads.
Centralization and Consolidation - Provide a central private cloud platform accessible to all relevant areas of the business.
When considering these objectives and use cases, what should the architect include in the design documentation as a part of the Conceptual Model?

A. An assumption that the new platform will co-exist with the existing platforms for a period of time to allow workloads to be migrated in a phased approach
B. An assumption that a complete mapping of application dependencies is not available
C. A risk that the existing platforms are running Linux Operating Systems that are out of vendor support
D. A requirement that the solution will provide the capability to migrate Kubernetes based workloads from the Public Cloud
 
Correct Answer: A

Question #42
An architect had gathered the following requirements and constraints for a VMware Cloud Foundation (VCF) deployment.
Requirements:
User interface (UI) SSL certificates must have a maximum validity of 6 months.
Have the least possible administrative time to install and renew certificates.
Each certificate must be created on a per VCF component basis.
Constraints:
Limited administrative skillsets on SSL certificate administration
Limited operational expenditure budget for SSL certificates
Which design decision should be made to satisfy the stated requirement(s) and constraint(s)?

A. Use and configure integration with a certificate vendor such as Digicert
B. Use wildcard certificates
C. Use and configure integration with Microsoft Certificate Authority (CA)
D. Disable the use of SSL certificates for user interfaces
 
Correct Answer: C

Question #43
During a requirements gathering workshop several Business and Technical requirements were captured from the customer.
Which requirement is classified as a Technical Requirement?

A. The system must support 5,000 concurrent users.
B. Increase customer satisfaction by 15%.
C. Expand market reach to include new geographical regions.
D. Reduce system processing time for service requests by 25%.
 
Correct Answer: A

Question #44
A VMware Cloud Foundation multi-AZ (Availability Zone) design mandates that:
All availability zones must operate independently of each other.
The availability SLA must adhere to no less than 99.9%.
What would be the three design decisions that would help satisfy those requirements? (Choose three.)

A. Make sure the recovery VLAN for the infrastructure management components has access to both AZ(s)
B. Choose two distant AZ(s) and consider each AZ the DR for the other
C. Make sure all configuration backups are replicated between the selected AZ(s)
D. Configure Array Based Replication between the selected AZ(s) for the management domain
E. Choose two close proximity AZ(s) and configure a stretched management workload domain
F. Configure a non-routable separate recovery VLAN for the infrastructure management components within each AZ
 
Correct Answer: BCD
Community vote distribution
ABC (100%)

Question #45
An administrator is designing a new VMware Cloud Foundation instance that has to support management, VDI, DB, and general workloads.
The DB workloads will stay the same in terms of resources over time. However, the general workloads and VDI environments are expected to grow over the next 3 years.
What should the architect include in the documentation?

A. A risk that the VCF instance may not have enough capacity for growth.
B. A requirement consisting of the growth of the general workloads and VDI environment. Most Voted
C. A constraint of including the management, DB, and VDI environments.
D. An assumption that the DB workload resource requirements will remain static.
 
Correct Answer: D
Community vote distribution
B (67%)
D (33%)

Question #46
A VMware Cloud Foundation (VCF) platform has been commissioned and lines of business are requesting approved virtual machines applications via the platforms integrated automation portal.
The platform was built following all provided company security guidelines and has been assessed against Sarbanes-Oxley Act of 2002 (SOX) regulations. The platform has the following characteristics:
One Management domain with a single cluster, supporting all management services with all network traffic handled by a single Distributed Virtual Switches (DVS).
A dedicated VI workload domain with a single cluster for all line of business applications.
A dedicated VI workload domain with a single cluster for Virtual Desktop Infrastructure (VDI).
Aria Operations is being used to monitor all clusters.
VI Workload domains are using a shared NSX instance.
An application owner has asked for approval to install a new service that must be protected as per the Payment Card Industry (PCI) Data Security Standard which is going to be verified by a third party organization.
To support the new service, which additional non-functional requirement should be added to the design?

A. The VCF platform and all PCI application virtual machines must be assessed for SOX compliance.
B. The VCF platform and all PCI application virtual machine network traffic must be routed via NSX.
C. The VCF platform and all PCI application virtual machines must be monitored using the Aria Operations Compliance Pack for Payment Card Industry.
D. The VCF platform and all PCI application virtual machines must be assessed against Payment Card Industry Data Security Standard (PCI-DSS) compliance.
 
Correct Answer: D

Question #47
An architect has come up with a list of design decisions after a workshop with the business stakeholders. Which design decision describes a logical design decision?

A. End users will interact with application server hosted in Site A. Most Voted
B. Asynchronous storage replication that satisfies a recovery point objective (RPO) of 15min between site A and B.
C. Both sites A and B will have a /16 dedicated network subnets.
D. End users should always experience instantaneous application response.
 
Correct Answer: B
Community vote distribution
A (50%)
B (50%)

Question #48
During a requirement capture workshop, the customer expressed a plan to use Aria Operations Continuous Availability. The customer identified two Datacenters that meet the network requirements to support Continuous Availability, however they are unsure which of the following Datacenters would be suitable for the Witness Node.
image1
Which Datacenter meets the minimum network requirements for the Witness Node?

A. Datacenter A
B. Datacenter D
C. Datacenter B
D. Datacenter C
 
Correct Answer: C

Question #49
During a security focused design workshop for a new VMware Cloud Foundation (VCF) solution, a key stakeholder described the current and potential future approach to user authentication within their organization.
The following information was captured by an architect:
All users within the organization currently have Active Directory backed user accounts.
A separate project is planned to evaluate the use of different 3rd party identity solutions to enforce Multi-Factor Authentication (MFA) on all user accounts.
The MFA project will only provide a recommendation on which identity solution the organization should implement.
The MFA project will need to request budget for any licenses that need to be procured for the recommended identity solution.
The new VCF environment may be deployed before the MFA project has completed and therefore must be able to integrate with both the current and any proposed future identity solutions.
Which two items should the architect include into their design documentation? (Choose two.)

A. An assumption that the MFA project will not receive budget to implement a new 3rd party identity solution
B. A risk that the new 3rd party identity solution may not be compatible with VCF
C. A risk that the new 3rd party identity solution may not be compatible with Active Directory Most Voted
D. A requirement that VCF will integrate only with the new 3rd party identity solution
E. An assumption that the new 3rd party identity solution will be compatible with VCF Most Voted
 
Correct Answer: BE
Community vote distribution
CE (100%)

Question #50
During a design discussion, the VMware Cloud Foundation Architect was presented with a requirement to reduce power utilization across all workload domains including management. The architect has suggested to use vSphere Distributed Power Management (DPM) to satisfy this requirement.
Which recommendation should the architect provide?

A. vSphere DPM for Management Workload Domain (only when hosted within a Hyperscaler Data Center).
B. vSphere DPM for Management Workload Domain (excluding when vSAN is a principal storage).
C. vSphere DPM for Management Workload Domain (any principal storage).
D. vSphere DPM for VI Workload Domains (any principal storage).
E. vSphere DPM for VI Workload Domains (excluding when vSAN is a principal storage).
 
Correct Answer: E

Question #51
During the requirements gathering workshop for a new VMware Cloud Foundation (VCF)-based Private Cloud solution, the customer states that the solution must:
Provide sufficient capacity to migrate and run their existing workloads.
Provide sufficient initial capacity to support a forecasted resource growth of 30% over the next 3 years.
When creating the design document, under which design quality should the architect classify these stated requirements?

A. Performance
B. Availability
C. Manageability
D. Recoverability
 
Correct Answer: A

Question #52
An architect is responsible for designing a new VMware Cloud Foundation environment and has identified the following requirements provided by the customer:
REQ01 The database server must support a minimum of 15,000 transactions per second.
REQ02 The design must satisfy PCI-DSS compliance.
REQ03 The storage network must have a minimum latency of 10 milliseconds prior to path failover.
REQ04 The Production environment must be deployed into the primary data center.
REQ05 The platform must be capable of running 1500 virtual machines across both data centers.
What are the two functional requirements? (Choose two.)

A. The Production environment must be deployed into the primary data center.
B. The database server must support a minimum of 15,000 transactions per second.
C. The platform must be capable of running 1500 virtual machines across both data centers. Most Voted
D. The storage network must have a minimum latency of 10 milliseconds prior to path failover.
E. The design must satisfy PCI-DSS compliance. Most Voted
 
Correct Answer: BC
Community vote distribution
CE (100%)

Question #53
During the requirements gathering workshop for a new VMware Cloud Foundation (VCF)-based Private Cloud solution, the customer states that the solution must:
Provide a single interface for the monitoring all components of the solution.
Minimize the effort required to maintain the solution to N-1 software versions.
When creating the design document, under which design quality should the architect classify these stated requirements?

A. Manageability
B. Recoverability
C. Performance
D. Availability
 
Correct Answer: A

Question #54
An Architect is responsible for the designing of a VMware Cloud Foundation (VCF)-based solution for a customer. During a discovery workshop, the following requirements were stated by the customer:
All applications/workloads designated as business critical have a Recovery Point Objective (RPO) of 1 business hour.
The infrastructure components of the VCF solution must have a Recovery Time Objective (RTO) of 4 business hours.
In the context provided, what does the RPO measure?

A. It determines the maximum tolerable amount of time allowed before an application/service should be recovered to a useable state.
B. It determines the minimum tolerable amount of time allowed before an application/service should be recovered to a useable state.
C. It determines the minimum amount of data loss that can be tolerated.
D. It determines the maximum amount of data loss that can be tolerated.
 
Correct Answer: D

Question #55
A customer has stated the following requirements for Aria Automation within their VCF implementation:
Users must have access to specific resources based on their company organization.
Developers must only be able to provision to the Development environment.
Production workloads can be placed on DMZ or Production clusters.
What two design decisions must be implemented to satisfy these requirements? (Choose two.)

A. Separate tenants will be configured for Development and Production. Most Voted
B. Users access to resources will be controlled by project membership.
C. Separate cloud zones will be configured for Development and Production.
D. Users access to resources will be controlled by tenant membership. Most Voted
 
Correct Answer: BC
Community vote distribution
AD (100%)

Question #56
An administrator is documenting the design for a new VMware Cloud Foundation (VCF) solution. During discovery workshops with the customer the following information was shared with the architect:
All users and administrators of the solution will need to be authenticated using accounts in the corporate directory service.
The solution will need to be deployed across two geographically separate locations and run in an Active/Standby configuration where supported.
The management applications deployed as part of the solution will need to be recovered to the standby location in the event of a disaster.
All management applications will need to be deployed into a management tooling zone of the network which is separated from the corporate network zone by multiple firewalls.
The corporate directory service is deployed in the corporate zone.
There is an internal organization policy which requires each application instance (management or end user) to detail the ports that access is required on through the firewall separately.
Firewall rule requests are processed manually one application instance at a time and typically take a minimum of 8 weeks to complete.
The customer also informed the architect that the new solution needs to be deployed and ready to start the organization's acceptance into service process within 3 months as it is a dependency in the deployment of a business critical application.
When considering the design for the Cloud Automation and Operations products within the VCF solution, which three design decisions should the architect include based on this information? (Choose three.)

A. The Cloud Automation and Operations products will be integrated with a single instance of an Identity Broker solution at the primary site.
B. The Cloud Automation and Operations products will be integrated directly with the corporate directory service.
C. The Identity Broker solution will be deployed at the primary site and failed over to the standby site in case of a disaster.
D. The Identity Broker solution will be deployed at both the primary and standby site.
E. The Identity Broker solution will be connected with the corporate directory service for user authentication.
F. The Cloud Automation and Operations products will be reconfigured to integrate with the Identity Broker solution instance at the standby site in case of a Disaster Recovery incident.
 
Correct Answer: ACE

Question #57
A customer is deploying VCF at a new datacenter location. They will migrate their workloads from the existing datacenter to the new VCF platform over six months. Both datacenters will run simultaneously for six months during the migration.
Which of the following should be a documented risk?

A. Six months may not be enough time to complete the migration.
B. There will be connectivity between the two locations.
C. Bandwidth between the two locations is sufficient to accommodate the workload migration.
D. Workloads will be powered off during migration.
 
Correct Answer: A

Question #58
An architect is designing a new VCF solution to meet the following requirements:
The solution must be deployed across two availability zones.
The physical hosts must be installed in a single rack per availability zone.
Workloads running in the cluster must be able to run on hosts in either availability zone.
The architect has decided that to meet these requirements the solution will be deployed using the Single Instance - Multiple Availability Zones VCF Topology.
When considering the design for the network, what should the architect include in the logical design to meet these requirements?

A. A physical network fabric supporting routed networks across the availability zones
B. A single NSX Overlay Transport Zone for all clusters to carry the traffic between the ESXi hosts
C. A pair of Cisco routers acting as the gateway within each of the availability zones
D. A 25-GbE port on each Top of Rack (ToR) switch connected to the ESXi host uplinks
 
Correct Answer: A

Question #59
Which Operating System (OS) is not supported by Aria Operations for OS and Application Monitoring?

A. MacOS
B. Windows Server 2012
C. CentOS
D. Windows Server 2012 R2
 
Correct Answer: A

Question #60
An architect is designing a VMware Cloud Foundation (VCF)-based private cloud solution for a customer that will include two physical locations. The customer has stated the following requirement:
All management tooling must be resilient at the component level within a single site.
When considering the design decisions for VMware Aria Suite components, what should the Architect document to meet the stated requirement?

A. The solution will deploy three Aria Automation appliances in a clustered configuration.
B. The solution will deploy Aria Suite Lifecycle Manager in a high availability configuration.
C. The solution will configure the VCF Workload domain in a stretched topology across two locations.
D. The solution will implement an external load balancer for Aria Operations Cloud Proxies.
 
Correct Answer: A